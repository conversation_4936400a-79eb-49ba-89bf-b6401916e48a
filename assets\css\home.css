/* --- CSS Reset & Global Styles --- */
:root {
    --primary-color: #c4a265;
    --dark-bg: #141414;
    --section-dark-bg: #101010;
    --services-articles-bg: #141318;
    --card-bg: #1f1f1f;
    --text-color: #d1d1d1;
    --heading-color: #ffffff;
    --font-heading: '<PERSON><PERSON>', sans-serif;
    --font-body: '<PERSON><PERSON>', sans-serif;
    --border-color: #333333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    background-color: var(--dark-bg);
    color: var(--text-color);
    font-size: 16px;
    line-height: 1.7;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-heading);
    color: var(--heading-color);
    font-weight: 600;
}

a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.section-padding {
    padding: 120px 0;
}

.section-subtitle {
    font-family: var(--font-body);
    color: var(--primary-color);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.section-title {
    font-size: 48px;
    margin-bottom: 40px;
    line-height: 1.2;
}

.btn {
    display: inline-block;
    padding: 16px 38px;
    font-family: var(--font-heading);
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 2px solid transparent;
    transition: all 0.4s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
}

.btn-dark {
    background-color: #0e0e0e;
    color: #fff;
    border: 2px solid #fff;
}

.btn-dark:hover {
    background-color: #fff;
    border-color: #fff;
    color: var(--dark-bg);
}

/* --- Header --- */
.main-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    padding: 25px 0;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 50px;
}

.logo img {
    max-width: 150px;
}

.main-nav ul {
    display: flex;
    gap: 40px;
}

.main-nav a {
    font-family: var(--font-heading);
    font-weight: 500;
    color: #fff;
    font-size: 16px;
}

.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 28px;
    cursor: pointer;
}

/* --- Hero Section --- */
.hero-section {
    height: 100vh;
    min-height: 800px;
    background: url('../images/header-1-scaled.jpg') no-repeat center center/cover;
    display: flex;
    align-items: center;
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(14, 14, 14, 0.6);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #fff;
}

.hero-content .section-subtitle {
    color: #fff;
}

.hero-content h1 {
    font-size: 85px;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 40px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.social-icons-vertical {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 5;
}

.social-icons-vertical a {
    color: #fff;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.05);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.social-icons-vertical a:hover {
    background: var(--primary-color);
    color: #fff;
}

/* --- About Section --- */
.about-section {
    background-color: var(--section-dark-bg);
}

.about-section .container {
    display: flex;
    align-items: center;
    gap: 80px;
}

.about-section .section-subtitle {
    color: var(--primary-color);
}

.about-section .section-title {
    color: var(--heading-color);
}

.about-section p {
    color: var(--text-color);
}

.about-images {
    flex: 0 0 50%;
    position: relative;
}

.about-images .img-1 {
    width: 80%;
    border: 15px solid var(--card-bg);
}

.about-images .img-2 {
    width: 65%;
    position: absolute;
    bottom: -60px;
    right: 0;
    border: 15px solid var(--card-bg);
}

.about-content {
    flex: 1;
}

.about-content p {
    margin-bottom: 25px;
}

.about-checklist {
    list-style: none;
    margin-bottom: 30px;
}

.about-checklist li {
    margin-bottom: 12px;
    padding-left: 25px;
    position: relative;
}

.about-checklist li::before {
    content: '\f00c';
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 6px;
    color: var(--primary-color);
    font-size: 14px;
}

.experience-box {
    background: url('../images/hero2.jpg') no-repeat center center/cover;
    padding: 25px 35px;
    display: inline-block;
    text-align: center;
    border: 10px solid var(--card-bg);
    box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
}

.experience-box .years {
    font-size: 50px;
    font-family: var(--font-heading);
    font-weight: 700;
    color: #fff;
    line-height: 1;
}

.experience-box .text {
    color: #fff;
    font-weight: 500;
    font-size: 16px;
}

.about-content .btn-primary {
    margin-top: 40px;
}

/* --- Services Section --- */
.services-section {
    background-color: var(--services-articles-bg);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 60px;
}

.service-card {
    background-color: var(--card-bg);
    padding: 50px 40px;
    text-align: left;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, border 0.3s ease;
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
}

.service-card .service-icon {
    margin-bottom: 25px;
}

.service-card .service-icon img {
    width: 50px;
    height: 50px;
}

.service-card h3 {
    font-size: 24px;
    margin-bottom: 15px;
}

.service-card .service-number {
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 100px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.03);
    line-height: 1;
}

/* --- Projects Section --- */
.projects-section {
    background-color: var(--section-dark-bg);
}

.projects-section .section-subtitle {
    color: var(--primary-color);
}

.projects-section .section-title {
    color: var(--heading-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: 300px;
    gap: 30px;
}

.project-item {
    overflow: hidden;
    position: relative;
}

.project-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-item:hover img {
    transform: scale(1.1);
}

.project-item .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(14, 14, 14, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.project-item:hover .project-overlay {
    opacity: 1;
}

.project-item .project-overlay a {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    transform: scale(0.8);
    transition: transform 0.4s ease;
}

.project-item:hover .project-overlay a {
    transform: scale(1);
}

.project-item:nth-child(1) {
    grid-row: span 1;
}

.project-item:nth-child(2) {
    grid-column: 2 / 3;
    grid-row: 1 / 3;
}

.project-item:nth-child(3) {
    grid-row: span 1;
}

.project-item:nth-child(4) {
    grid-row: span 1;
}

.project-item:nth-child(5) {
    grid-row: span 1;
}

/* --- CTA Section --- */
.cta-section {
    background: url('../images/header-1-scaled.jpg') no-repeat center center/cover;
    background-attachment: fixed;
    padding: 120px 0;
    text-align: center;
    position: relative;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(14, 14, 14, 0.8);
}

.cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-section .section-title {
    font-size: 52px;
    line-height: 1.3;
    margin-bottom: 40px;
}

/* --- Testimonials Section --- */
.testimonials-section {
    background-color: var(--section-dark-bg);
}

.testimonial-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 50px;
}

.testimonial-nav {
    display: flex;
    gap: 10px;
}

.swiper-button-prev,
.swiper-button-next {
    position: relative;
    width: 50px;
    height: 50px;
    background-color: var(--card-bg);
    color: var(--primary-color);
    transition: all 0.3s ease;
    margin: 0;
}

.swiper-button-prev:hover,
.swiper-button-next:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.swiper-button-prev::after,
.swiper-button-next::after {
    font-size: 16px;
    font-weight: 900;
}

.testimonial-card {
    background-color: var(--card-bg);
    padding: 50px 40px;
    text-align: left;
    position: relative;
}

.testimonial-card::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50px;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid var(--card-bg);
}

.testimonial-card::before {
    content: "\f10d";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    top: 30px;
    right: 30px;
    font-size: 80px;
    color: rgba(255, 255, 255, 0.04);
    z-index: 0;
    line-height: 1;
}

.testimonial-card-content {
    position: relative;
    z-index: 1;
}

.testimonial-rating {
    color: #f39c12;
    margin-bottom: 20px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.testimonial-author img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
}

.author-info h4 {
    font-size: 20px;
    margin-bottom: 0px;
}

.author-info span {
    font-size: 14px;
    color: var(--primary-color);
}

/* --- Articles Section --- */
.articles-section {
    background-color: var(--services-articles-bg);
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.article-card {
    background-color: var(--card-bg);
}

.article-content {
    padding: 30px;
}

.article-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #aaa;
}

.article-meta i {
    margin-right: 5px;
    color: var(--primary-color);
}

.article-card h3 {
    font-size: 22px;
    margin-bottom: 20px;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.article-card:hover h3 {
    color: var(--primary-color);
}

.article-card .read-more-arrow {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
}

.article-card:hover .read-more-arrow {
    background-color: #fff;
    color: var(--primary-color);
}

.article-image {
    overflow: hidden;
}

.article-image img {
    transition: transform 0.5s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.1);
}

/* --- Newsletter Section --- */
.newsletter-section {
    background-color: var(--section-dark-bg);
    padding: 80px 0;
}

.newsletter-section .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.newsletter-section h3 {
    color: var(--heading-color);
}

.newsletter-section p {
    color: var(--text-color);
}

.newsletter-content h3 {
    font-size: 28px;
    margin-bottom: 5px;
    color: var(--heading-color);
}

.newsletter-form {
    display: flex;
    width: 45%;
}

.newsletter-form input {
    flex: 1;
    padding: 16px 20px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    font-family: var(--font-body);
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.newsletter-form input::placeholder {
    color: #999;
}

.newsletter-form .btn {
    border-radius: 0;
    border-width: 1px;
}

/* --- Footer --- */
.main-footer {
    background-color: var(--section-dark-bg);
    padding-top: 120px;
}

.footer-widgets {
    display: grid;
    grid-template-columns: 2.5fr 1fr 1.5fr 2fr;
    gap: 30px;
    padding-bottom: 80px;
    border-bottom: 1px solid var(--border-color);
}

.footer-widget h4 {
    font-size: 22px;
    margin-bottom: 30px;
}

.footer-widget .logo {
    margin-bottom: 25px;
}

.footer-socials {
    display: flex;
    gap: 10px;
    margin-top: 25px;
}

.footer-socials a {
    width: 45px;
    height: 45px;
    background-color: var(--card-bg);
    display: flex;
    justify-content: center;
    align-items: center;
}

.footer-list li {
    margin-bottom: 15px;
}

.footer-list a {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-list i {
    color: var(--primary-color);
    transition: transform 0.3s ease, color 0.3s ease;
}

.footer-list a:hover i {
    transform: translateX(5px);
    color: #fff;
}

.contact-info li {
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
}

.contact-info strong {
    color: var(--primary-color);
}

.contact-info li::before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 2px;
    color: var(--primary-color);
}

.contact-info li.phone::before {
    content: "\f095";
}

.contact-info li.email::before {
    content: "\f0e0";
}

.contact-info li.location::before {
    content: "\f3c5";
}

.copyright-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 0;
}

.back-to-top {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
}

/* --- Responsive Design --- */
@media (max-width: 1200px) {
    .footer-widgets {
        grid-template-columns: repeat(2, 1fr);
        gap: 50px;
    }
}

@media (max-width: 992px) {
    .section-padding {
        padding: 100px 0;
    }

    .section-title {
        font-size: 40px;
    }

    .hero-content h1 {
        font-size: 60px;
    }

    .main-nav {
        display: none;
    }

    .header-container .btn {
        display: none;
    }

    .mobile-nav-toggle {
        display: block;
    }

    .about-section .container {
        flex-direction: column;
        text-align: center;
    }

    .about-images {
        margin-bottom: 80px;
        flex-basis: auto;
    }

    .about-checklist li {
        padding-left: 0;
        text-align: left;
    }

    .about-checklist li::before {
        display: none;
    }

    /* Hide icon, simple list on mobile */

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .projects-grid {
        grid-template-columns: 1fr 1fr;
        grid-auto-rows: 250px;
    }

    .project-item:nth-child(2) {
        grid-column: auto;
        grid-row: auto;
    }

    .testimonial-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .testimonial-nav {
        margin-top: 20px;
    }

    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .newsletter-section .container {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .newsletter-form {
        width: 100%;
        max-width: 500px;
    }
}

@media (max-width: 768px) {
    .section-padding {
        padding: 80px 0;
    }

    .section-title {
        font-size: 36px;
    }

    .hero-content h1 {
        font-size: 48px;
    }

    .social-icons-vertical {
        display: none;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .footer-widgets {
        grid-template-columns: 1fr;
    }

    .testimonial-header {
        align-items: center;
    }

    .testimonial-header .section-title {
        text-align: center;
        margin-bottom: 20px;
    }

    .copyright-area {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}